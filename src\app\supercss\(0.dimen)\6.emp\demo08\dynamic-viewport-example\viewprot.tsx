import { motion, useMotionValueEvent, useScroll } from "motion/react";
import { useRef, useState, forwardRef, useImperativeHandle, useEffect } from "react";
import { Stripes } from "../stripes";
import { useIsMobile } from './useMobileDetection';
import { House, RefreshCw } from "lucide-react";

const viewport = {
  lvh: 491,  // Large Viewport Height - 最大可能高度（地址栏隐藏时）
  navBarHeight: 48,
  width: 300,  // 当前容器宽度
};

const transition = {
  duration: 0.2,
  ease: "linear",
  type: "tween",
};

// Props 接口定义
interface Props {
  children?: React.ReactNode;
  transType?: "rotation" | "swap";
  onChange?: (hidden: boolean) => void;
  onLandscapeChange?: (isLandscape: boolean) => void
  isHeader?: boolean;
}

export const Viewport = forwardRef<HTMLDivElement, Props>(
  ({ children, transType = "swap", onChange, onLandscapeChange, isHeader = true }, ref) => {
    const [hidden, setHidden] = useState(!isHeader);
    const [rotationAngle, setRotationAngle] = useState(0);
    const [isSwapped, setIsSwapped] = useState(false);
    const parent = useRef<HTMLDivElement>(null);
    const isMobile = useIsMobile();
    const { scrollY } = useScroll({ container: parent });

    // 将内部 ref 暴露给外部
    useImperativeHandle(ref, () => parent.current!, []);

    // 根据变换类型计算容器是否处于"宽高交换"状态
    const isDimensionSwapped = transType === "rotation" ? rotationAngle === 90 : isSwapped;

    const animationVariants = {
      visible: {
        y: 0,
        height: isDimensionSwapped ? `${viewport.width}px` : `${viewport.lvh}px`
      },
      hidden: {
        y: `-${viewport.navBarHeight - 2}px`,
        height: isDimensionSwapped ? `${viewport.width + viewport.navBarHeight - 1}px` : `${viewport.lvh + viewport.navBarHeight - 1}px`
      }
    }

    // 处理变换操作：根据 transType 执行不同的逻辑
    const handleRotate = () => {
      if (transType === "rotation") {
        // 旋转模式：执行真实的 CSS transform rotate 旋转变换
        setRotationAngle(!rotationAngle ? 90 : 0);
      } else {
        // 交换模式：切换宽高交换状态，不执行旋转变换
        setIsSwapped(!isSwapped);
      }

    };

    useEffect(() => {
      onLandscapeChange?.(isDimensionSwapped);
    }, [isDimensionSwapped, onLandscapeChange]);

    useMotionValueEvent(scrollY, "change", (latest) => {
      const previous = scrollY.getPrevious() ?? 0;
      let newHidden;

      if (latest > previous) {
        newHidden = true;
        setHidden(true);
      } else {
        newHidden = false;
        setHidden(false);
      }

      onChange?.(newHidden);
    });

    return (
      <div className="relative grid justify-items-center">
        <motion.div style={{ transformOrigin: "center center" }} animate={{ rotate: transType === "rotation" ? -rotationAngle : 0 }} transition={{ ...transition }}>
          <Stripes ref={parent}
            style={{
              width: isMobile ? `calc(100vw - 8vw)` : `${isDimensionSwapped ? viewport.lvh : viewport.width}px`,
              height: `${isDimensionSwapped ? viewport.width : viewport.lvh}px`,
              transform: transType === "rotation" && rotationAngle === 90 ? 'rotate(90deg)' : 'none',
              transformOrigin: 'center center',
              transition: 'width 0.3s ease-in-out, height 0.3s ease-in-out',
            }}
            className="relative overflow-y-scroll overscroll-none rounded-lg border border-slate-300 text-center text-xs dark:border-slate-700 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
            <motion.div
              className="absolute w-full snap-start overflow-hidden"
              transition={{ ...transition }}
              variants={animationVariants}
              initial="visible"
              animate={hidden ? "hidden" : "visible"}
            >
              <header className="grid h-[48px] w-full grid-cols-[auto_1fr_auto] items-center justify-start gap-4 rounded-t-lg border-b border-slate-300 bg-slate-100 px-3 dark:border-slate-600 dark:bg-slate-800">
                <House className="h-5 w-5 text-slate-500 dark:text-slate-400" />
                <div className="w-full rounded-full border border-slate-200 bg-slate-50 px-4 py-1 text-slate-600 dark:border-slate-700 dark:bg-slate-700 dark:text-slate-400">usehook.cn</div>
                <button onClick={handleRotate} disabled={isMobile} className="flex items-center justify-center h-6 w-6 rounded-full bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500 transition-colors">
                  <RefreshCw className="h-4 w-4 text-slate-600 dark:text-slate-400" />
                </button>
              </header>
              {children}
            </motion.div>
          </Stripes>
        </motion.div>
      </div>
    );
  }
);

Viewport.displayName = 'Viewport';
