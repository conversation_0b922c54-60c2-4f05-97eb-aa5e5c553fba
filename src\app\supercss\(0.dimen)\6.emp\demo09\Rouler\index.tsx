import { clsx } from "clsx";
import { isValidElement, cloneElement, useRef, useState } from "react";
import useChildrenDimensions from "../useChildrenDimensions";

interface Offset {
  start: number;
  end: number;
}

interface RoulerConfig {
  position: 'left' | 'right' | 'top' | 'bottom';
  value: string;
  offset: Offset;

}

interface RoulerProps {
  vertical?: RoulerConfig;
  horizontal?: RoulerConfig;
  children: React.ReactNode;
}

// Rouler 首先就是单纯的一个容器组件
// 首先入参 vertical horizontal 两个方向的配置， children 
// 使用的时候就只需要传入一个 比如 vertical 就可以 ，然后传入 children 
// 然后我们根据传入的配置，来渲染出对应的样式即可
// 比如 vertical = { position: 'bottom', value: '100px', offset: { start: 48, end: 0 }}   就是尺度 显示在底部就是代表高度， 然后 value 就是高度的值， offset 就是起始和结束的偏移量
// 否则 horizontal = { position: 'right', value: '100px', offset: { start: 48, end: 0 }}   就是尺度 显示在右边就是代表宽度， 然后 value 就是宽度的值， offset 就是起始和结束的偏移量
// 布局的话如何让尺度线条放在 右边 或者 底部 都是相同的布局，只是方向不同而已 应该如何实现？ 
// 以及 Rouler 的位置如何根据 position 来确定呢？



export default function Rouler({ vertical, horizontal, children }: RoulerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isAddressBarVisible, setIsAddressBarVisible] = useState(false);
  const { width, height } = useChildrenDimensions(containerRef as React.RefObject<HTMLDivElement>);

  //判断两种情况 地址栏隐藏 和 地址栏未隐藏的情况 隐藏的时候高度是 491px 未隐藏的时候高度是 443px 
  const getLineHeight = isAddressBarVisible ? height - 48 : height;

  const getRoulerStyle = (config: RoulerConfig): React.CSSProperties => {
    const baseStyle = { transition: 'all 0.1s linear' }
    const { position } = config;
    if (position === 'left') {
      return {
        ...baseStyle,
        position: 'absolute' as const,
        left: '0px',
        top: isAddressBarVisible ? '48px' : '0px',
        transform: 'translateX(-100%)',
        height: getLineHeight,
      }
    }
    return {};
  }

  const renderRuler = (config: RoulerConfig) => {
    const { position, value, offset } = config;
    return (
      <div className={clsx('flex justify-center items-center flex-col')} style={{ ...getRoulerStyle(config) }}>
        <div className={clsx('w-4 h-1 bg-amber-300')}></div>
        <div className={clsx('w-px bg-amber-300 flex-1')}></div>
        <div>11</div>
        <div className={clsx(' w-px bg-amber-300 flex-1')}></div>
        <div className={clsx(' w-4 h-1 bg-amber-300')}></div>
      </div>
    )
  }

  const handleAddressBarChange = (value: boolean) => {
    setIsAddressBarVisible(!value);
  }
  return (
    <div className="relative w-fit mx-auto">
      <div ref={containerRef} className="relative">
        {isValidElement(children)
          ? cloneElement(children, {
            onChange: handleAddressBarChange
          } as any)
          : children
        }

        {vertical && renderRuler(vertical)}
        {horizontal && renderRuler(horizontal)}
      </div>
    </div>
  );
}
