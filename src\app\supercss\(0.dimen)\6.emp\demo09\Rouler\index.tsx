import { clsx } from "clsx";
import { isValidElement, cloneElement, useRef, useState, useEffect } from "react";
import useChildrenDimensions from "../useChildrenDimensions";

interface Offset {
  start: number;
  end: number;
}

interface RoulerConfig {
  position: 'left' | 'right' | 'top' | 'bottom';
  value: string;
  offset: Offset;

}

interface RoulerProps {
  vertical?: RoulerConfig;
  horizontal?: RoulerConfig;
  children: React.ReactNode;
}

// Rouler 首先就是单纯的一个容器组件
// 首先入参 vertical horizontal 两个方向的配置， children 
// 使用的时候就只需要传入一个 比如 vertical 就可以 ，然后传入 children 
// 然后我们根据传入的配置，来渲染出对应的样式即可
// 比如 vertical = { position: 'bottom', value: '100px', offset: { start: 48, end: 0 }}   就是尺度 显示在底部就是代表高度， 然后 value 就是高度的值， offset 就是起始和结束的偏移量
// 否则 horizontal = { position: 'right', value: '100px', offset: { start: 48, end: 0 }}   就是尺度 显示在右边就是代表宽度， 然后 value 就是宽度的值， offset 就是起始和结束的偏移量
// 布局的话如何让尺度线条放在 右边 或者 底部 都是相同的布局，只是方向不同而已 应该如何实现？ 
// 以及 Rouler 的位置如何根据 position 来确定呢？



export default function Rouler({ vertical, horizontal, children }: RoulerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isAddressBarVisible, setIsAddressBarVisible] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);
  const { width, height } = useChildrenDimensions(containerRef as React.RefObject<HTMLDivElement>);

  // 监听屏幕方向变化
  useEffect(() => {
    const checkOrientation = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
    };

    checkOrientation();
    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);

    return () => {
      window.removeEventListener('resize', checkOrientation);
      window.removeEventListener('orientationchange', checkOrientation);
    };
  }, []);

  //判断两种情况 地址栏隐藏 和 地址栏未隐藏的情况 隐藏的时候高度是 491px 未隐藏的时候高度是 443px
  const getLineHeight = isAddressBarVisible ? height - 48 : height;
  const getLineWidth = width;

  const getRoulerStyle = (config: RoulerConfig): React.CSSProperties => {
    const { position, offset } = config;
    const baseStyle: React.CSSProperties = {
      position: 'absolute' as const,
      zIndex: 10,
    };

    switch (position) {
      case 'left':
        return {
          ...baseStyle,
          left: '0px',
          top: `${offset.start}px`,
          transform: 'translateX(-100%)',
          height: getLineHeight - offset.start - offset.end,
        };
      case 'right':
        return {
          ...baseStyle,
          right: '0px',
          top: `${offset.start}px`,
          transform: 'translateX(100%)',
          height: getLineHeight - offset.start - offset.end,
        };
      case 'top':
        return {
          ...baseStyle,
          top: '0px',
          left: `${offset.start}px`,
          transform: 'translateY(-100%)',
          width: getLineWidth - offset.start - offset.end,
        };
      case 'bottom':
        return {
          ...baseStyle,
          bottom: '0px',
          left: `${offset.start}px`,
          transform: 'translateY(100%)',
          width: getLineWidth - offset.start - offset.end,
        };
      default:
        return baseStyle;
    }
  }

  const renderRuler = (config: RoulerConfig) => {
    const { position, value } = config;
    const isVertical = position === 'left' || position === 'right';
    const isHorizontal = position === 'top' || position === 'bottom';

    // 根据方向调整布局和样式
    const containerClasses = isVertical
      ? 'flex justify-center items-center flex-col'
      : 'flex justify-center items-center flex-row';

    const lineClasses = isVertical
      ? 'w-px bg-amber-300 flex-1'
      : 'h-px bg-amber-300 flex-1';

    const capClasses = isVertical
      ? 'w-4 h-1 bg-amber-300'
      : 'w-1 h-4 bg-amber-300';

    // 在横屏模式下，如果是水平辅助线，显示宽度信息
    const displayValue = isHorizontal && isLandscape
      ? `${Math.round(width)}px`
      : value;

    return (
      <div className={clsx(containerClasses)} style={{ ...getRoulerStyle(config) }}>
        <div className={clsx(capClasses)}></div>
        <div className={clsx(lineClasses)}></div>
        <div className="text-xs text-amber-600 font-medium px-1 py-0.5 bg-white/80 rounded shadow-sm whitespace-nowrap">
          {displayValue}
        </div>
        <div className={clsx(lineClasses)}></div>
        <div className={clsx(capClasses)}></div>
      </div>
    )
  }

  const handleAddressBarChange = (value: boolean) => {
    setIsAddressBarVisible(!value);
  }
  return (
    <div className="relative w-fit mx-auto">
      <div ref={containerRef} className="relative">
        {isValidElement(children)
          ? cloneElement(children, {
            onChange: handleAddressBarChange
          } as unknown as React.HTMLAttributes<HTMLElement>)
          : children
        }

        {vertical && renderRuler(vertical)}
        {horizontal && renderRuler(horizontal)}
      </div>
    </div>
  );
}
