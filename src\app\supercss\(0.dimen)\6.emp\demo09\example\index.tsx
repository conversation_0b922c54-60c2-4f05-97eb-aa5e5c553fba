'use client'

import { Viewport } from "../../demo08/dynamic-viewport-example/viewprot";
import Rouler from "../Rouler";

export default function Example() {
  return (
    <div className="p-8 bg-gray-100 min-h-screen space-y-8">
      {/* 左侧垂直辅助线示例 */}
      <div className="border-2 border-red-500 p-4">
        <h3 className="text-lg font-semibold mb-4">左侧垂直辅助线</h3>
        <Rouler vertical={{ position: 'left', value: '高度', offset: { start: 0, end: 0 }}} >
          <Viewport>
            <div className="p-4 bg-blue-100">
              左侧辅助线示例内容
            </div>
          </Viewport>
        </Rouler>
      </div>

      {/* 右侧垂直辅助线示例 */}
      <div className="border-2 border-green-500 p-4">
        <h3 className="text-lg font-semibold mb-4">右侧垂直辅助线</h3>
        <Rouler vertical={{ position: 'right', value: '高度', offset: { start: 20, end: 20 }}} >
          <Viewport>
            <div className="p-4 bg-green-100">
              右侧辅助线示例内容
            </div>
          </Viewport>
        </Rouler>
      </div>

      {/* 顶部水平辅助线示例 */}
      <div className="border-2 border-blue-500 p-4">
        <h3 className="text-lg font-semibold mb-4">顶部水平辅助线</h3>
        <Rouler horizontal={{ position: 'top', value: '宽度', offset: { start: 10, end: 10 }}} >
          <Viewport>
            <div className="p-4 bg-yellow-100">
              顶部辅助线示例内容
            </div>
          </Viewport>
        </Rouler>
      </div>

      {/* 底部水平辅助线示例 */}
      <div className="border-2 border-purple-500 p-4">
        <h3 className="text-lg font-semibold mb-4">底部水平辅助线</h3>
        <Rouler horizontal={{ position: 'bottom', value: '宽度', offset: { start: 0, end: 0 }}} >
          <Viewport>
            <div className="p-4 bg-purple-100">
              底部辅助线示例内容
            </div>
          </Viewport>
        </Rouler>
      </div>

      {/* 同时显示垂直和水平辅助线 */}
      <div className="border-2 border-orange-500 p-4">
        <h3 className="text-lg font-semibold mb-4">垂直 + 水平辅助线</h3>
        <Rouler
          vertical={{ position: 'left', value: '高度', offset: { start: 0, end: 0 }}}
          horizontal={{ position: 'bottom', value: '宽度', offset: { start: 0, end: 0 }}}
        >
          <Viewport>
            <div className="p-4 bg-orange-100">
              同时显示垂直和水平辅助线的示例
            </div>
          </Viewport>
        </Rouler>
      </div>

      <div className="text-sm text-gray-600 mt-8">
        <p>💡 提示：</p>
        <ul className="list-disc list-inside space-y-1">
          <li>在横屏模式下，水平辅助线会自动显示实际宽度值</li>
          <li>offset 参数可以控制辅助线的起始和结束偏移量</li>
          <li>支持四个方向：left、right、top、bottom</li>
          <li>辅助线会根据屏幕方向自动调整显示</li>
        </ul>
      </div>
    </div>
  );
}
