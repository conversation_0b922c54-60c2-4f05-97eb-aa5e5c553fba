import { Viewport } from "../dynamic-viewport-example/viewprot";
import { motion } from "motion/react";
import { useEffect, useRef, useState } from "react";
import { clsx } from "clsx";

const transition = {
  duration: 0.2,
  ease: "linear",
  type: "tween",
} as const;

// DVW 专用配置 - 动态响应，有延迟
const dvwConfig = {
  delay: 0.4,
  getWidth: (parentWidth: number) => parentWidth,
  getHeight: (parentHeight: number, isVisible: boolean) => isVisible ? parentHeight - 48 : parentHeight,
  bgColor: "bg-green-500 dark:bg-green-500",
  borderColor: "border-green-400",
};

export default function DvwDemo({ w = 100 }) {
  const [hidden, setHidden] = useState(false);
  const [IsDimensionSwapped, setIsDimensionSwapped] = useState(false);
  const parentRef = useRef<HTMLDivElement>(null);
  const [parentwh, setParentwh] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!parentRef.current) return;

    const rect = parentRef.current.getBoundingClientRect();
    setParentwh({ width: rect.width, height: rect.height });

    const timer = setTimeout(() => {
      if (!parentRef.current) return;
      const rect = parentRef.current.getBoundingClientRect();
      setParentwh({ width: rect.width, height: rect.height });
    }, 300);

    return () => clearTimeout(timer);
  }, [IsDimensionSwapped]);

  return (
    <Viewport onChange={setHidden} onLandscapeChange={setIsDimensionSwapped} ref={parentRef}>
      <motion.div
        className="pointer-events-none h-full w-full p-[7px]"
        transition={{ ...transition, delay: dvwConfig.delay }}
        variants={{
          visible: {
            maxWidth: `${dvwConfig.getWidth(parentwh.width) * w / 100}px`,
            maxHeight: `${dvwConfig.getHeight(parentwh.height, true)}px`,
          },
          hidden: {
            maxWidth: `${dvwConfig.getWidth(parentwh.width) * w / 100}px`,
            maxHeight: `${dvwConfig.getHeight(parentwh.height, false)}px`,
          },
        }}
        initial="visible"
        animate={hidden ? "hidden" : "visible"}
      >
        <div className={clsx("border grid h-full w-full content-center items-center justify-items-center self-center overflow-hidden rounded-md py-4 font-mono font-bold text-slate-50",
          // 动态调整grid布局和gap
          w >= 80 ? "grid-cols-[1fr_auto_1fr] gap-5" :
            w >= 60 ? "grid-cols-[1fr_auto_1fr] gap-3" :
              "grid-cols-[minmax(20px,1fr)_auto_minmax(20px,1fr)] gap-2",
          dvwConfig.bgColor,
          dvwConfig.borderColor
        )}>
          <div className={clsx("grid w-full items-center", w >= 60 ? "grid-cols-[1px_1fr] px-4" : "grid-cols-[1px_1fr] px-2")}>
            <div className="w-full h-[12px] bg-white/60"></div>
            <div className="w-full h-[1.5px] bg-white/40"></div>
          </div>
          <p className={clsx("text-center", w >= 60 ? "whitespace-nowrap" : "text-xs leading-tight")}> {w}dvw </p>
          <div className={clsx("grid w-full items-center", w >= 60 ? "grid-cols-[1fr_1px] px-4" : "grid-cols-[1fr_1px] px-2")}>
            <div className="w-full h-[1.5px] bg-white/40"></div>
            <div className="w-full h-[12px] bg-white/60"></div>
          </div>
        </div>
      </motion.div>
    </Viewport>
  );
}
