import { Viewport } from "../dynamic-viewport-example/viewprot";
import { motion } from "motion/react";
import { useEffect, useRef, useState } from "react";
import { clsx } from "clsx";

const transition = {
  duration: 0.2,
  ease: "linear",
  type: "tween",
} as const;

const dvhConfig = {
  delay: 0.4,
  getHeight: (parentHeight: number, isVisible: boolean) => isVisible ? parentHeight - 48 : parentHeight,
  bgColor: "bg-blue-500 dark:bg-blue-500",
  borderColor: "border-blue-400",
};

export default function DvhDemo({ h = 100 }) {
  const [hidden, setHidden] = useState(false);
  const [IsDimensionSwapped, setIsDimensionSwapped] = useState(false);
  const parentRef = useRef<HTMLDivElement>(null);
  const [parentwh, setParentwh] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!parentRef.current) return;

    const rect = parentRef.current.getBoundingClientRect();
    setParentwh({ width: rect.width, height: rect.height });

    const timer = setTimeout(() => {
      if (!parentRef.current) return;
      const rect = parentRef.current.getBoundingClientRect();
      setParentwh({ width: rect.width, height: rect.height });
    }, 300);

    return () => clearTimeout(timer);
  }, [IsDimensionSwapped]);

  return (
    <Viewport onChange={setHidden} onLandscapeChange={setIsDimensionSwapped} ref={parentRef} isHeader={false}>
      <motion.div className="pointer-events-none h-full w-full p-[7px]" transition={{ ...transition, delay: dvhConfig.delay }}
        variants={{
          visible: { maxHeight: `${dvhConfig.getHeight(parentwh.height, true) * h / 100}px`, },
          hidden: { maxHeight: `${dvhConfig.getHeight(parentwh.height, false) * h / 100}px`, },
        }}
        initial="visible"
        animate={hidden ? "hidden" : "visible"}
      >
        <div className={clsx("border grid h-full w-full content-center items-center justify-items-center self-center overflow-hidden rounded-md py-4 font-mono font-bold text-slate-50",
          // 动态调整grid布局和gap
          h >= 80 ? "grid-rows-[1fr_auto_1fr] gap-5" :
            h >= 60 ? "grid-rows-[1fr_auto_1fr] gap-3" :
              "grid-rows-[minmax(20px,1fr)_auto_minmax(20px,1fr)] gap-2",
          dvhConfig.bgColor,
          dvhConfig.borderColor
        )}>
          <div className={clsx("grid h-full justify-items-center", h >= 60 ? "grid-rows-[1px_1fr] py-4" : "grid-rows-[1px_1fr] py-2")}>
            <div className="h-full w-[12px] bg-white/60"></div>
            <div className="h-full w-[1.5px] bg-white/40"></div>
          </div>
          <p className={clsx("text-center", h >= 60 ? "whitespace-nowrap" : "text-xs leading-tight")}> {h}dvh </p>
          <div className={clsx("grid h-full justify-items-center", h >= 60 ? "grid-rows-[1fr_1px] py-4" : "grid-rows-[1fr_1px] py-2")}>
            <div className="h-full w-[1.5px] bg-white/40"></div>
            <div className="h-full w-[12px] bg-white/60"></div>
          </div>
        </div>
      </motion.div>
    </Viewport>
  );
}
