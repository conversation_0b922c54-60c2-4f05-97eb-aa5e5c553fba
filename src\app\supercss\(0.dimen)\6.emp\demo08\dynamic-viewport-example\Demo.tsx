import { Viewport } from "./viewprot";
import { motion } from "motion/react";
import { useEffect, useRef, useState } from "react";
import { clsx } from "clsx";

const transition = {
  duration: 0.2,
  ease: "linear",
  type: "tween",
};

// 单位配置
const unitConfigs = {
  dvh: {
    delay: 0.4,
    getHeight: (parentHeight: number, isVisible: boolean) =>
      isVisible ? parentHeight : parentHeight - 48
  },
  dvw: {
    delay: 0.4,
    getHeight: (parentHeight: number, isVisible: boolean) =>
      isVisible ? parentHeight : parentHeight - 48
  },
  lvh: {
    delay: 0,
    getHeight: (parentHeight: number) => parentHeight
  },
  lvw: {
    delay: 0,
    getHeight: (parentHeight: number) => parentHeight
  },
  svh: {
    delay: 0,
    getHeight: (parentHeight: number) => parentHeight - 48
  },
  svw: {
    delay: 0,
    getHeight: (parentHeight: number) => parentHeight - 48
  }
};

const getUnitColors = (unit: string) => {
  const colorMap = {
    dvh: "bg-blue-500 dark:bg-blue-500 border-blue-400",
    dvw: "bg-green-500 dark:bg-green-500 border-green-400",
    lvh: "bg-purple-500 dark:bg-purple-500 border-purple-400",
    lvw: "bg-pink-500 dark:bg-pink-500 border-pink-400",
    svh: "bg-orange-500 dark:bg-orange-500 border-orange-400",
    svw: "bg-red-500 dark:bg-red-500 border-red-400"
  };
  return colorMap[unit as keyof typeof colorMap] || colorMap.dvh;
};

export default function Demo({ unit = 'dvh' }) {
  const [hidden, setHidden] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);
  const parentRef = useRef<HTMLDivElement>(null);
  const [parentwh, setParentwh] = useState({ width: 0, height: 0 });

  // 获取当前单位的配置
  const config = unitConfigs[unit as keyof typeof unitConfigs] || unitConfigs.dvh;

  useEffect(() => {
    if (!parentRef.current) return;

    const rect = parentRef.current.getBoundingClientRect();
    setParentwh({ width: rect.width, height: rect.height });

    const timer = setTimeout(() => {
      if (!parentRef.current) return;
      const rect = parentRef.current.getBoundingClientRect();
      setParentwh({ width: rect.width, height: rect.height });
    }, 300);

    return () => clearTimeout(timer);
  }, [isLandscape]);

  return (
    <Viewport onChange={setHidden} onLandscapeChange={setIsLandscape} ref={parentRef}>
      <motion.div
        className="pointer-events-none h-full w-full p-[7px]"
        transition={{ ...transition, delay: config.delay }}
        variants={{
          visible: {
            maxHeight: `${config.getHeight(parentwh.height, hidden)}px`,
          },
          hidden: {
            maxHeight: `${config.getHeight(parentwh.height, hidden)}px`,
          },
        }}
        initial="visible"
        animate={hidden ? "hidden" : "visible"}
      >
        <div className={clsx(
          "border grid h-full w-full grid-rows-[1fr_auto_1fr] content-center items-center justify-items-center gap-5 self-center overflow-hidden rounded-md py-4 font-mono font-bold text-slate-50",
          getUnitColors(unit)
        )}>
          <div className="grid h-full grid-rows-[1px_1fr] justify-items-center">
            <div className="h-full w-[12px] bg-white/60"></div>
            <div className="h-full w-[1.5px] bg-white/40"></div>
          </div>
          <p>{unit.endsWith('h') ? 'h-' + unit : 'w-' + unit}</p>
          <div className="grid h-full grid-rows-[1fr_1px] justify-items-center">
            <div className="h-full w-[1.5px] bg-white/40"></div>
            <div className="h-full w-[12px] bg-white/60"></div>
          </div>
        </div>
      </motion.div>
    </Viewport >
  )
}
