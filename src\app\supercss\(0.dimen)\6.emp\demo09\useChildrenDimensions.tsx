// 首先需要一个 elref
// 其次 使用observe 监听 elref 的尺寸变化
// 然后返回一个对象，包含 width height

import { useState, useLayoutEffect } from "react";

export default function useChildrenDimensions(elRef: React.RefObject<HTMLDivElement>) {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // 使用 useLayoutEffect 确保在 DOM 更新后立即获取尺寸
  useLayoutEffect(() => {
    const el = elRef.current;
    if (!el) return;

    // 边界处理 渲染立即获取一次尺寸
    const rect = el.getBoundingClientRect();
    setDimensions({ width: rect.width, height: rect.height });

    const observer = new ResizeObserver((entries) => {
      const { width, height } = entries[0].contentRect;
      setDimensions({ width, height });
    });

    observer.observe(el);
    return () => observer.disconnect();
  }, [elRef]);

  return dimensions;
}
