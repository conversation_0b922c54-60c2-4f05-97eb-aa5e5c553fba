import { Viewport } from "../dynamic-viewport-example/viewprot";
import { motion } from "motion/react";
import { useEffect, useRef, useState } from "react";
import { clsx } from "clsx";

const transition = {
  duration: 0.2,
  ease: "linear",
  type: "tween",
} as const;

const lvwConfig = {
  delay: 0,
  getWidth: (parentWidth: number) => parentWidth, // 始终使用完整宽度
  bgColor: "bg-pink-500 dark:bg-pink-500",
  borderColor: "border-pink-400",
};

export default function LvwDemo({ w = 100 }) {
  const [hidden, setHidden] = useState(false);
  const [IsDimensionSwapped, setIsDimensionSwapped] = useState(false);
  const parentRef = useRef<HTMLDivElement>(null);
  const [parentwh, setParentwh] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!parentRef.current) return;

    const rect = parentRef.current.getBoundingClientRect();
    setParentwh({ width: rect.width, height: rect.height });

    const timer = setTimeout(() => {
      if (!parentRef.current) return;
      const rect = parentRef.current.getBoundingClientRect();
      setParentwh({ width: rect.width, height: rect.height });
    }, 300);

    return () => clearTimeout(timer);
  }, [IsDimensionSwapped]);

  return (
    <div className="relative grid justify-items-center">
      <Viewport onChange={setHidden} onLandscapeChange={setIsDimensionSwapped} ref={parentRef}>
        <motion.div
          className="pointer-events-none h-full w-full p-[7px]"
          transition={{ ...transition, delay: lvwConfig.delay }}
          variants={{
            visible: {
              maxWidth: `${lvwConfig.getWidth(parentwh.width) * w / 100}px`,
              maxHeight: `${parentwh.height}px`,
            },
            hidden: {
              maxWidth: `${lvwConfig.getWidth(parentwh.width) * w / 100}px`, // LVW 始终保持最大宽度
              maxHeight: `${parentwh.height}px`,
            },
          }}
          initial="visible"
          animate={hidden ? "hidden" : "visible"}
        >
          <div className={clsx("border grid h-full w-full content-center items-center justify-items-center self-center overflow-hidden rounded-md py-4 font-mono font-bold text-slate-50",
            // 动态调整grid布局和gap
            w >= 80 ? "grid-cols-[1fr_auto_1fr] gap-5" :
              w >= 60 ? "grid-cols-[1fr_auto_1fr] gap-3" :
                "grid-cols-[minmax(20px,1fr)_auto_minmax(20px,1fr)] gap-2",
            lvwConfig.bgColor,
            lvwConfig.borderColor
          )}>
            <div className={clsx("grid w-full items-center", w >= 60 ? "grid-cols-[1px_1fr] px-4" : "grid-cols-[1px_1fr] px-2")}>
              <div className="w-full h-[12px] bg-white/60"></div>
              <div className="w-full h-[1.5px] bg-white/40"></div>
            </div>
            <p className={clsx("text-center", w >= 60 ? "whitespace-nowrap" : "text-xs leading-tight")}> {w}lvw </p>
            <div className={clsx("grid w-full items-center", w >= 60 ? "grid-cols-[1fr_1px] px-4" : "grid-cols-[1fr_1px] px-2")}>
              <div className="w-full h-[1.5px] bg-white/40"></div>
              <div className="w-full h-[12px] bg-white/60"></div>
            </div>
          </div>
        </motion.div>
      </Viewport>
      <motion.div
        transition={{ ...transition }}
        variants={{
          visible: {
            bottom: `-${48}px`,
            height: `${48}px`,
            width: `${parentwh.width - 16}px`,
          },
          hidden: {
            bottom: "5px",
            height: "0px",
            width: `${parentwh.width - 16}px`,
          },
        }}
        initial="visible"
        animate={hidden ? "hidden" : "visible"}
        className={clsx("dark:bg-purple-500 bg-purple-500 border border-purple-400", "absolute right-0 left-0 mx-auto rounded-b-md opacity-20")}
      />
    </div>
  );
}
